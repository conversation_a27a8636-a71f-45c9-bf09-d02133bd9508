#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理器模块

提供多层次缓存功能，支持内存缓存和持久化文件缓存，
包含TTL机制、压缩存储和缓存统计功能。

作者: Assistant
日期: 2024
"""

import os
import pickle
import gzip
import time
import logging
import hashlib
from pathlib import Path
from typing import Any, Dict, Optional, Set, Tuple
from dataclasses import dataclass, field
from threading import Lock


@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    timestamp: float
    access_count: int = 0
    last_access: float = field(default_factory=time.time)
    
    def is_expired(self, ttl: float) -> bool:
        """检查是否过期"""
        return time.time() - self.timestamp > ttl
    
    def touch(self):
        """更新访问时间和计数"""
        self.last_access = time.time()
        self.access_count += 1


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    memory_entries: int = 0
    file_entries: int = 0
    total_size_bytes: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def reset(self):
        """重置统计"""
        self.hits = 0
        self.misses = 0
        self.evictions = 0


class CacheManager:
    """多层次缓存管理器"""
    
    def __init__(
        self,
        cache_dir: Path,
        memory_ttl: float = 3600,  # 内存缓存TTL (秒)
        file_ttl: float = 86400,   # 文件缓存TTL (秒)
        max_memory_entries: int = 10000,
        enable_compression: bool = True,
        enable_file_cache: bool = True
    ):
        self.cache_dir = Path(cache_dir)
        self.memory_ttl = memory_ttl
        self.file_ttl = file_ttl
        self.max_memory_entries = max_memory_entries
        self.enable_compression = enable_compression
        self.enable_file_cache = enable_file_cache
        
        # 内存缓存
        self._memory_cache: Dict[str, CacheEntry] = {}
        self._memory_lock = Lock()
        
        # 统计信息
        self.stats = CacheStats()
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 确保缓存目录存在
        if self.enable_file_cache:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"缓存目录: {self.cache_dir}")
    
    def _generate_key(self, key: str) -> str:
        """生成缓存键的哈希值"""
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    
    def _get_file_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        hashed_key = self._generate_key(key)
        return self.cache_dir / f"{hashed_key}.cache"
    
    def _save_to_file(self, key: str, data: Any) -> bool:
        """保存数据到文件缓存"""
        if not self.enable_file_cache:
            return False
            
        try:
            file_path = self._get_file_path(key)
            cache_entry = CacheEntry(data=data, timestamp=time.time())
            
            if self.enable_compression:
                with gzip.open(file_path, 'wb') as f:
                    pickle.dump(cache_entry, f)
            else:
                with open(file_path, 'wb') as f:
                    pickle.dump(cache_entry, f)
            
            return True
        except Exception as e:
            self.logger.warning(f"保存文件缓存失败 {key}: {e}")
            return False
    
    def _load_from_file(self, key: str) -> Optional[CacheEntry]:
        """从文件缓存加载数据"""
        if not self.enable_file_cache:
            return None
            
        try:
            file_path = self._get_file_path(key)
            if not file_path.exists():
                return None
            
            if self.enable_compression:
                with gzip.open(file_path, 'rb') as f:
                    cache_entry = pickle.load(f)
            else:
                with open(file_path, 'rb') as f:
                    cache_entry = pickle.load(f)
            
            # 检查是否过期
            if cache_entry.is_expired(self.file_ttl):
                file_path.unlink(missing_ok=True)
                return None
            
            return cache_entry
        except Exception as e:
            self.logger.warning(f"加载文件缓存失败 {key}: {e}")
            return None
    
    def _cleanup_memory_cache(self):
        """清理内存缓存中的过期条目"""
        with self._memory_lock:
            current_time = time.time()
            expired_keys = [
                key for key, entry in self._memory_cache.items()
                if entry.is_expired(self.memory_ttl)
            ]
            
            for key in expired_keys:
                del self._memory_cache[key]
                self.stats.evictions += 1
            
            # 如果超过最大条目数，删除最少使用的条目
            if len(self._memory_cache) > self.max_memory_entries:
                # 按访问时间排序，删除最旧的条目
                sorted_items = sorted(
                    self._memory_cache.items(),
                    key=lambda x: x[1].last_access
                )
                
                excess_count = len(self._memory_cache) - self.max_memory_entries
                for key, _ in sorted_items[:excess_count]:
                    del self._memory_cache[key]
                    self.stats.evictions += 1
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        # 首先检查内存缓存
        with self._memory_lock:
            if key in self._memory_cache:
                entry = self._memory_cache[key]
                if not entry.is_expired(self.memory_ttl):
                    entry.touch()
                    self.stats.hits += 1
                    return entry.data
                else:
                    del self._memory_cache[key]
                    self.stats.evictions += 1
        
        # 检查文件缓存
        file_entry = self._load_from_file(key)
        if file_entry:
            # 将文件缓存数据加载到内存缓存
            with self._memory_lock:
                self._memory_cache[key] = file_entry
                file_entry.touch()
            self.stats.hits += 1
            return file_entry.data
        
        self.stats.misses += 1
        return None
    
    def set(self, key: str, data: Any) -> bool:
        """设置缓存数据"""
        try:
            # 清理过期的内存缓存
            self._cleanup_memory_cache()
            
            # 保存到内存缓存
            cache_entry = CacheEntry(data=data, timestamp=time.time())
            with self._memory_lock:
                self._memory_cache[key] = cache_entry
            
            # 保存到文件缓存
            if self.enable_file_cache:
                self._save_to_file(key, data)
            
            return True
        except Exception as e:
            self.logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存数据"""
        deleted = False
        
        # 从内存缓存删除
        with self._memory_lock:
            if key in self._memory_cache:
                del self._memory_cache[key]
                deleted = True
        
        # 从文件缓存删除
        if self.enable_file_cache:
            file_path = self._get_file_path(key)
            if file_path.exists():
                file_path.unlink()
                deleted = True
        
        return deleted
    
    def clear(self):
        """清空所有缓存"""
        # 清空内存缓存
        with self._memory_lock:
            self._memory_cache.clear()
        
        # 清空文件缓存
        if self.enable_file_cache and self.cache_dir.exists():
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink(missing_ok=True)
        
        # 重置统计
        self.stats.reset()
        self.logger.info("缓存已清空")
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._memory_lock:
            self.stats.memory_entries = len(self._memory_cache)
        
        if self.enable_file_cache and self.cache_dir.exists():
            self.stats.file_entries = len(list(self.cache_dir.glob("*.cache")))
        
        return self.stats
    
    def cleanup_expired(self):
        """清理所有过期的缓存条目"""
        # 清理内存缓存
        self._cleanup_memory_cache()
        
        # 清理文件缓存
        if self.enable_file_cache and self.cache_dir.exists():
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    # 尝试加载并检查是否过期
                    if self.enable_compression:
                        with gzip.open(cache_file, 'rb') as f:
                            entry = pickle.load(f)
                    else:
                        with open(cache_file, 'rb') as f:
                            entry = pickle.load(f)
                    
                    if entry.is_expired(self.file_ttl):
                        cache_file.unlink()
                        self.stats.evictions += 1
                except Exception:
                    # 如果文件损坏，直接删除
                    cache_file.unlink(missing_ok=True)
        
        self.logger.info("过期缓存清理完成")
