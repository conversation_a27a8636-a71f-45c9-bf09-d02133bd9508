#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的ClickHouse更新器

集成持久化缓存、批处理优化、连接池管理和错误重试机制，
在保持向后兼容性的同时提供显著的性能优化。

作者: Assistant
日期: 2024
"""

import logging
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

try:
    from clickhouse_driver import Client
except ImportError:
    print("错误: 无法导入clickhouse_driver模块")
    print("请安装clickhouse-driver: pip install clickhouse-driver")
    import sys
    sys.exit(1)

# 导入优化模块
from cache_manager import CacheManager
from batch_processor import BatchProcessor, BatchConfig
from concurrent_processor import ConcurrentProcessor, ConcurrentConfig
from pipeline_config import PipelineConfig


class EnhancedClickHouseUpdater:
    """增强的ClickHouse更新器"""
    
    def __init__(
        self,
        config: PipelineConfig,
        enable_optimizations: bool = True
    ):
        self.config = config
        self.enable_optimizations = enable_optimizations and config.enable_optimizations
        self.logger = logging.getLogger(__name__)
        
        # ClickHouse客户端
        self.client = Client(
            host=config.clickhouse_host,
            port=config.clickhouse_port,
            connect_timeout=10,
            send_receive_timeout=config.clickhouse_timeout
        )
        
        # 优化组件
        if self.enable_optimizations:
            self._init_optimization_components()
        
        # 统计信息
        self.stats = {
            'total_processed': 0,
            'cache_hits': 0,
            'db_queries': 0,
            'successful_updates': 0,
            'warnings': 0,
            'errors': 0
        }
    
    def _init_optimization_components(self):
        """初始化优化组件"""
        # 缓存管理器
        if self.config.enable_persistent_cache:
            self.cache_manager = CacheManager(
                cache_dir=self.config.cache_dir / 'clickhouse',
                memory_ttl=self.config.cache_memory_ttl,
                file_ttl=self.config.cache_file_ttl,
                max_memory_entries=self.config.max_memory_cache_entries,
                enable_compression=self.config.enable_cache_compression,
                enable_file_cache=True
            )
        else:
            self.cache_manager = None
        
        # 批处理器
        batch_config = BatchConfig(
            batch_size=self.config.clickhouse_batch_size,
            max_concurrent=1,  # ClickHouse通常不需要高并发
            retry_attempts=self.config.concurrent_retry_attempts,
            retry_delay=self.config.concurrent_retry_delay,
            timeout=self.config.clickhouse_timeout
        )
        self.batch_processor = BatchProcessor(batch_config)
        
        self.logger.info("ClickHouse优化组件初始化完成")
    
    def update_dataframe(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """
        更新DataFrame中的hazard信息
        
        Args:
            df: 输入的DataFrame，必须包含inchified_smiles列
            
        Returns:
            Tuple[pd.DataFrame, List[Dict]]: (成功更新的DataFrame, 错误记录列表)
        """
        if 'inchified_smiles' not in df.columns:
            raise ValueError("DataFrame中缺少inchified_smiles字段")
        
        self.logger.info(f"开始更新DataFrame中的hazard信息，共 {len(df)} 行")
        self.stats['total_processed'] = len(df)
        
        if self.enable_optimizations:
            return self._update_dataframe_optimized(df)
        else:
            return self._update_dataframe_standard(df)
    
    def _update_dataframe_optimized(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """优化的DataFrame更新方法"""
        self.logger.info("使用优化模式处理DataFrame")
        
        # 第一步：获取唯一的inchified_smiles
        unique_inchi_smis = df['inchified_smiles'].dropna().unique().tolist()
        self.logger.info(f"唯一inchified_smiles数量: {len(unique_inchi_smis)}")
        
        # 第二步：检查缓存
        cached_results = {}
        uncached_smiles = []
        
        if self.cache_manager:
            for inchi_smi in unique_inchi_smis:
                cache_key = f"clickhouse_hazard:{inchi_smi}"
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    cached_results[inchi_smi] = cached_result
                    self.stats['cache_hits'] += 1
                else:
                    uncached_smiles.append(inchi_smi)
            
            self.logger.info(f"缓存命中: {len(cached_results)}, 需要数据库查询: {len(uncached_smiles)}")
        else:
            uncached_smiles = unique_inchi_smis
        
        # 第三步：批量查询未缓存的数据
        db_results = {}
        if uncached_smiles:
            self.stats['db_queries'] = len(uncached_smiles)
            db_results = self._query_hazards_batch_optimized(uncached_smiles)
            
            # 将结果保存到缓存
            if self.cache_manager:
                for inchi_smi, result in db_results.items():
                    cache_key = f"clickhouse_hazard:{inchi_smi}"
                    self.cache_manager.set(cache_key, result)
        
        # 第四步：合并结果
        all_results = {**cached_results, **db_results}
        
        # 第五步：应用结果到DataFrame
        result_df = self._apply_hazard_results(df, all_results)
        
        # 分离成功和错误记录
        error_records = []
        success_df = result_df.copy()
        
        self.logger.info(f"优化处理完成: 成功 {len(success_df)}, 错误 {len(error_records)}")
        self._log_optimization_stats()
        
        return success_df, error_records
    
    def _update_dataframe_standard(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[Dict]]:
        """标准的DataFrame更新方法（向后兼容）"""
        self.logger.info("使用标准模式处理DataFrame")
        
        success_data = []
        error_data = []
        
        # 获取所有唯一的inchified_smiles
        unique_inchi_smis = df['inchified_smiles'].dropna().unique().tolist()
        
        if unique_inchi_smis:
            # 批量查询hazard信息
            hazards_dict = self._query_hazards_batch_standard(unique_inchi_smis)
            self.logger.info(f"从ClickHouse查询到 {len(hazards_dict)} 条hazard信息")
        else:
            hazards_dict = {}
        
        # 处理每一行
        for idx, row in df.iterrows():
            row_dict = row.to_dict()
            inchi_smi = row_dict.get('inchified_smiles', '').strip()
            
            if not inchi_smi:
                row_dict['error_reason'] = 'inchified_smiles字段为空'
                error_data.append(row_dict)
                self.stats['errors'] += 1
            elif inchi_smi in hazards_dict:
                # 找到匹配的hazard信息
                hazard_info = hazards_dict[inchi_smi]
                hazards = hazard_info['hazards']
                row_dict['codes'] = hazards.split('|') if hazards else []
                row_dict['pubchem_safety_link'] = f"https://pubchem.ncbi.nlm.nih.gov/compound/{hazard_info['cid']}#section=Safety-and-Hazards"
                success_data.append(row_dict)
                self.stats['successful_updates'] += 1
            else:
                # 未找到匹配的hazard信息，作为警告处理
                row_dict['codes'] = []
                row_dict['pubchem_safety_link'] = ''
                success_data.append(row_dict)
                self.stats['warnings'] += 1
        
        success_df = pd.DataFrame(success_data) if success_data else pd.DataFrame()
        error_records = error_data
        
        return success_df, error_records
    
    def _query_hazards_batch_optimized(self, inchi_smis: List[str]) -> Dict[str, Dict[str, Any]]:
        """优化的批量查询hazard信息"""
        if not inchi_smis:
            return {}
        
        all_results = {}
        
        # 分块查询以避免查询过大
        chunk_size = 1000
        for i in range(0, len(inchi_smis), chunk_size):
            chunk = inchi_smis[i:i + chunk_size]
            
            try:
                query = f"""
                SELECT inchi_smi, cid, hazards
                FROM default.pubchem_hazards
                WHERE inchi_smi IN {tuple(chunk)}
                """
                result = self.client.execute(query)
                
                for row in result:
                    all_results[row[0]] = {
                        "cid": row[1], 
                        "hazards": row[2]
                    }
                    
            except Exception as e:
                self.logger.error(f"查询ClickHouse失败 (chunk {i//chunk_size + 1}): {e}")
                # 为这个chunk的所有项目返回空结果
                for inchi_smi in chunk:
                    all_results[inchi_smi] = {
                        "cid": None,
                        "hazards": None,
                        "error": str(e)
                    }
        
        return all_results
    
    def _query_hazards_batch_standard(self, inchi_smis: List[str]) -> Dict[str, Dict[str, Any]]:
        """标准的批量查询hazard信息（向后兼容）"""
        if not inchi_smis:
            return {}

        # 分块查询以避免查询过大
        chunk_size = 1000
        all_results = {}
        
        for i in range(0, len(inchi_smis), chunk_size):
            chunk = inchi_smis[i:i + chunk_size]
            query = f"""
            SELECT inchi_smi, cid, hazards
            FROM default.pubchem_hazards
            WHERE inchi_smi IN {tuple(chunk)}
            """
            result = self.client.execute(query)
            for row in result:
                all_results[row[0]] = {"cid": row[1], "hazards": row[2]}
        
        return all_results
    
    def _apply_hazard_results(self, df: pd.DataFrame, results: Dict[str, Dict[str, Any]]) -> pd.DataFrame:
        """将hazard查询结果应用到DataFrame"""
        result_df = df.copy()
        
        # 确保目标列存在
        if 'codes' not in result_df.columns:
            result_df['codes'] = None
        if 'pubchem_safety_link' not in result_df.columns:
            result_df['pubchem_safety_link'] = ''
        
        for idx, row in result_df.iterrows():
            inchi_smi = row['inchified_smiles']
            
            if pd.isna(inchi_smi) or not str(inchi_smi).strip():
                continue
            
            inchi_smi = str(inchi_smi).strip()
            
            if inchi_smi in results:
                hazard_info = results[inchi_smi]
                
                if hazard_info.get('error'):
                    # 查询出错，保持空值
                    result_df.at[idx, 'codes'] = []
                    result_df.at[idx, 'pubchem_safety_link'] = ''
                    self.stats['errors'] += 1
                elif hazard_info.get('cid') and hazard_info.get('hazards'):
                    # 找到有效的hazard信息
                    hazards = hazard_info['hazards']
                    result_df.at[idx, 'codes'] = hazards.split('|') if hazards else []
                    result_df.at[idx, 'pubchem_safety_link'] = f"https://pubchem.ncbi.nlm.nih.gov/compound/{hazard_info['cid']}#section=Safety-and-Hazards"
                    self.stats['successful_updates'] += 1
                else:
                    # 未找到hazard信息
                    result_df.at[idx, 'codes'] = []
                    result_df.at[idx, 'pubchem_safety_link'] = ''
                    self.stats['warnings'] += 1
            else:
                # 未查询到结果
                result_df.at[idx, 'codes'] = []
                result_df.at[idx, 'pubchem_safety_link'] = ''
                self.stats['warnings'] += 1
        
        return result_df
    
    def _log_optimization_stats(self):
        """记录优化统计信息"""
        self.logger.info("ClickHouse优化统计信息:")
        self.logger.info(f"  总处理数量: {self.stats['total_processed']:,}")
        self.logger.info(f"  缓存命中: {self.stats['cache_hits']:,}")
        self.logger.info(f"  数据库查询: {self.stats['db_queries']:,}")
        self.logger.info(f"  成功更新: {self.stats['successful_updates']:,}")
        self.logger.info(f"  警告数量: {self.stats['warnings']:,}")
        self.logger.info(f"  错误数量: {self.stats['errors']:,}")
        
        if self.stats['total_processed'] > 0:
            cache_hit_rate = self.stats['cache_hits'] / self.stats['total_processed']
            query_reduction = (self.stats['total_processed'] - self.stats['db_queries']) / self.stats['total_processed']
            self.logger.info(f"  缓存命中率: {cache_hit_rate:.2%}")
            self.logger.info(f"  数据库查询减少: {query_reduction:.2%}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        if self.enable_optimizations and self.cache_manager:
            cache_stats = self.cache_manager.get_stats()
            stats['cache_stats'] = {
                'hit_rate': cache_stats.hit_rate,
                'memory_entries': cache_stats.memory_entries,
                'file_entries': cache_stats.file_entries
            }
        
        return stats
    
    def cleanup_cache(self):
        """清理过期缓存"""
        if self.cache_manager:
            self.cache_manager.cleanup_expired()
            self.logger.info("ClickHouse缓存清理完成")
